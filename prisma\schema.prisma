generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id                 String                   @id @default(cuid())
  name               String
  createdAt          DateTime                 @default(now())
  updatedAt          DateTime                 @updatedAt
  routing_rules      routing_rules[]
  routing_strategies routing_strategies[]
  skillsCatalog      SkillsCatalog[]
  sla_policies       sla_policies[]
  tasks              Task[]
  team_metrics       team_metrics[]
  users              User[]
  settings           OrganizationSettings?

  @@map("organizations")
}

model User {
  id                 String               @id @default(cuid())
  name               String
  email              String               @unique
  password           String?
  role               UserRole             @default(AGENT)
  organizationId     String
  maxConcurrentTasks Int                  @default(5)
  currentTaskCount   Int                  @default(0)
  status             UserStatus           @default(AVAILABLE)
  emailVerified      DateTime?
  image              String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  default_timezone   String?
  accounts           Account[]
  agent_availability agent_availability[]
  agent_metrics      agent_metrics[]
  performance        AgentPerformance?
  skills             AgentSkill[]
  sessions           Session[]
  taskEvents         TaskEvent[]
  task_metrics       task_metrics[]
  assignedTasks      Task[]
  organization       Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  working_hours      AgentWorkingHours[]
  availability       AgentAvailability[]

  @@index([organizationId])
  @@index([organizationId, status])
  @@index([email])
  @@map("users")
}

model Task {
  id                String              @id @default(cuid())
  organizationId    String
  title             String
  description       String?
  priority          TaskPriority        @default(MEDIUM)
  type              String              @default("general")
  estimatedDuration Int?
  source            String              @default("manual")
  assignedTo        String?
  assignedAt        DateTime?
  status            TaskStatus          @default(PENDING)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  metadata          Json?               @default("{}")
  applied_rules     Json?
  breached_sla      Boolean             @default(false)
  resolve_by        DateTime?
  resolved_at       DateTime?
  responded_at      DateTime?
  response_by       DateTime?
  sla_policy_id     String?
  escalations       escalations[]
  rule_executions   rule_executions[]
  events            TaskEvent[]
  task_metrics      task_metrics?
  requiredSkills    TaskRequiredSkill[]
  assignedUser      User?               @relation(fields: [assignedTo], references: [id])
  organization      Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  sla_policies      sla_policies?       @relation(fields: [sla_policy_id], references: [id])

  @@index([organizationId])
  @@index([organizationId, status])
  @@index([assignedTo])
  @@index([status])
  @@index([sla_policy_id])
  @@map("tasks")
}

model TaskEvent {
  id            String        @id @default(cuid())
  taskId        String
  userId        String?
  type          TaskEventType
  data          Json?
  createdAt     DateTime      @default(now())
  escalation_id String?
  task          Task          @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user          User?         @relation(fields: [userId], references: [id])

  @@index([taskId])
  @@index([createdAt])
  @@map("task_events")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model AgentSkill {
  id               String   @id @default(cuid())
  agentId          String   @map("agent_id")
  skillName        String   @map("skill_name")
  proficiencyLevel Int      @default(1) @map("proficiency_level")
  createdAt        DateTime @default(now()) @map("created_at")
  agent            User     @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([agentId, skillName])
  @@map("agent_skills")
}

model TaskRequiredSkill {
  id            String   @id @default(cuid())
  taskId        String   @map("task_id")
  skillName     String   @map("skill_name")
  requiredLevel Int      @default(1) @map("required_level")
  createdAt     DateTime @default(now()) @map("created_at")
  task          Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@unique([taskId, skillName])
  @@map("task_required_skills")
}

model SkillsCatalog {
  id             String       @id @default(cuid())
  organizationId String       @map("organization_id")
  name           String
  description    String?
  category       String?
  isActive       Boolean      @default(true) @map("is_active")
  createdAt      DateTime     @default(now()) @map("created_at")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, name])
  @@map("skills_catalog")
}

model agent_availability {
  id             String     @id
  agent_id       String
  status         UserStatus
  status_message String?
  start_time     DateTime   @default(now())
  end_time       DateTime?
  is_scheduled   Boolean    @default(false)
  created_at     DateTime   @default(now())
  updated_at     DateTime
  users          User       @relation(fields: [agent_id], references: [id], onDelete: Cascade)

  @@index([agent_id, status])
}

model agent_metrics {
  id                  String   @id
  agent_id            String
  period              String
  start_date          DateTime
  end_date            DateTime
  tasks_assigned      Int      @default(0)
  tasks_completed     Int      @default(0)
  tasks_escalated     Int      @default(0)
  total_work_time     Int      @default(0)
  active_time         Int      @default(0)
  avg_response_time   Float    @default(0)
  avg_handle_time     Float    @default(0)
  completion_rate     Float    @default(0)
  quality_score       Float    @default(0)
  sla_compliance_rate Float    @default(0)
  users               User     @relation(fields: [agent_id], references: [id], onDelete: Cascade)

  @@unique([agent_id, period, start_date])
  @@index([agent_id, period])
}

model escalations {
  id           String    @id
  task_id      String
  level        Int
  reason       String
  status       String
  escalated_to String?
  escalated_at DateTime  @default(now())
  resolved_at  DateTime?
  comment      String?
  created_at   DateTime  @default(now())
  updated_at   DateTime
  tasks        Task      @relation(fields: [task_id], references: [id], onDelete: Cascade)

  @@index([task_id, status])
}

model routing_rules {
  id              String       @id
  organization_id String
  name            String
  description     String?
  priority        Int          @default(0)
  is_active       Boolean      @default(true)
  conditions      Json
  actions         Json
  created_at      DateTime     @default(now())
  updated_at      DateTime
  organizations   Organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)

  @@index([organization_id, priority])
}

model routing_strategies {
  id              String       @id
  organization_id String
  name            String
  strategy        String
  is_default      Boolean      @default(false)
  is_active       Boolean      @default(true)
  configuration   Json         @default("{}")
  created_at      DateTime     @default(now())
  organizations   Organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)

  @@unique([organization_id, name])
}

model rule_executions {
  id         String   @id
  rule_id    String
  task_id    String
  succeeded  Boolean  @default(true)
  details    Json?
  created_at DateTime @default(now())
  tasks      Task     @relation(fields: [task_id], references: [id], onDelete: Cascade)

  @@index([task_id])
}

model sla_policies {
  id              String       @id
  organization_id String
  name            String
  description     String?
  priority        String
  response_time   Int
  resolution_time Int
  escalationRules Json
  is_active       Boolean      @default(true)
  created_at      DateTime     @default(now())
  updated_at      DateTime
  organizations   Organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  tasks           Task[]

  @@unique([organization_id, name])
}

model task_metrics {
  id                String    @id
  task_id           String    @unique
  assigned_agent_id String?
  created_at        DateTime  @default(now())
  assigned_at       DateTime?
  first_response_at DateTime?
  resolved_at       DateTime?
  wait_time         Int?
  handle_time       Int?
  total_time        Int?
  sla_policy        String?
  response_target   Int?
  resolve_target    Int?
  met_response_sla  Boolean?
  met_resolve_sla   Boolean?
  complexity        Int?
  quality           Int?
  customer_rating   Int?
  users             User?     @relation(fields: [assigned_agent_id], references: [id])
  tasks             Task      @relation(fields: [task_id], references: [id], onDelete: Cascade)

  @@index([assigned_agent_id])
  @@index([task_id])
}

model team_metrics {
  id                    String       @id
  organization_id       String
  period                String
  start_date            DateTime
  end_date              DateTime
  total_tasks           Int          @default(0)
  completed_tasks       Int          @default(0)
  escalated_tasks       Int          @default(0)
  avg_response_time     Float        @default(0)
  avg_handle_time       Float        @default(0)
  sla_compliance_rate   Float        @default(0)
  customer_satisfaction Float        @default(0)
  active_agents         Int          @default(0)
  utilization_rate      Float        @default(0)
  organizations         Organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)

  @@unique([organization_id, period, start_date])
  @@index([organization_id, period])
}

model AgentWorkingHours {
  id        String   @id @default(cuid())
  agentId   String   @map("agent_id")
  dayOfWeek Int      @map("day_of_week") // 0=Sunday, 1=Monday, etc.
  startTime String   @map("start_time")  // "09:00"
  endTime   String   @map("end_time")    // "17:00"
  timezone  String   @default("UTC")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([agentId, dayOfWeek])
  @@map("agent_working_hours")
}

model AgentAvailability {
  id          String    @id @default(cuid())
  agentId     String    @map("agent_id")
  status      String    @default("available") // 'available', 'busy', 'away', 'offline'
  reason      String?   // Optional reason for unavailability
  startTime   DateTime? @map("start_time")    // For temporary status changes
  endTime     DateTime? @map("end_time")      // When status should revert
  isTemporary Boolean   @default(false) @map("is_temporary")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("agent_availability_new")
}

model OrganizationSettings {
  id                    String   @id @default(cuid())
  organizationId        String   @unique @map("organization_id")
  defaultTimezone       String   @default("UTC") @map("default_timezone")
  businessHoursStart    String   @default("09:00") @map("business_hours_start")
  businessHoursEnd      String   @default("17:00") @map("business_hours_end")
  businessDays          Json     @default("[1,2,3,4,5]") // Monday-Friday
  afterHoursRouting     Boolean  @default(false) @map("after_hours_routing")
  weekendRouting        Boolean  @default(false) @map("weekend_routing")
  urgentTasksOverride   Boolean  @default(true) @map("urgent_tasks_override")
  overrideAgentHours    Boolean  @default(false) @map("override_agent_hours")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("organization_settings")
}

model AgentPerformance {
  id                  String   @id @default(cuid())
  agentId             String   @unique @map("agent_id")
  avgResponseTime     Int      @default(0) @map("avg_response_time") // minutes
  avgResolutionTime   Int      @default(0) @map("avg_resolution_time") // minutes
  completionRate      Float    @default(0.0) @map("completion_rate") // 0.0-1.0
  qualityScore        Float    @default(0.0) @map("quality_score") // 0.0-100.0
  totalTasksCompleted Int      @default(0) @map("total_tasks_completed")
  totalTasksAssigned  Int      @default(0) @map("total_tasks_assigned")
  lastUpdated         DateTime @default(now()) @map("last_updated")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("agent_performance")
}

model AgentPerformance {
  id                  String   @id @default(cuid())
  agentId             String   @unique @map("agent_id")
  avgResponseTime     Int      @default(0) @map("avg_response_time") // minutes
  avgResolutionTime   Int      @default(0) @map("avg_resolution_time") // minutes
  completionRate      Float    @default(0.0) @map("completion_rate") // 0.0-1.0
  qualityScore        Float    @default(0.0) @map("quality_score") // 0.0-100.0
  totalTasksCompleted Int      @default(0) @map("total_tasks_completed")
  totalTasksAssigned  Int      @default(0) @map("total_tasks_assigned")
  lastUpdated         DateTime @default(now()) @map("last_updated")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("agent_performance")
}

enum UserStatus {
  AVAILABLE
  BUSY
  AWAY
  OFFLINE
}

enum UserRole {
  ADMIN
  AGENT
}

enum TaskStatus {
  PENDING
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  ESCALATED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TaskEventType {
  CREATED
  ASSIGNED
  STATUS_CHANGED
  UPDATED
  COMPLETED
  ESCALATED
  COMMENT_ADDED
}
